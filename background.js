// Background script for LinkedIn Auto Connect & Message extension

class LinkedInAutomationBackground {
    constructor() {
        this.isRunning = false;
        this.currentCampaign = null;
        this.dailyLimits = new Map();
        
        this.init();
    }

    init() {
        // Listen for extension installation
        chrome.runtime.onInstalled.addListener(() => {
            this.initializeStorage();
        });

        // Listen for messages from popup and content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Listen for tab updates to inject content script
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url && tab.url.includes('linkedin.com')) {
                this.injectContentScript(tabId);
            }
        });

        // Reset daily limits at midnight
        this.setupDailyReset();
    }

    async initializeStorage() {
        const defaultData = {
            campaigns: [],
            profiles: [],
            templates: [
                {
                    id: '1',
                    name: 'Professional Introduction',
                    content: 'Hi {firstName}, I came across your profile and was impressed by your experience in {industry}. I\'d love to connect and potentially collaborate.',
                    active: true,
                    createdAt: new Date().toISOString()
                }
            ],
            settings: {
                dailyLimit: 20,
                delayMin: 30,
                delayMax: 60,
                autoConnect: true,
                autoMessage: false
            },
            analytics: {
                totalSent: 0,
                totalAccepted: 0,
                todaySent: 0,
                lastResetDate: new Date().toDateString()
            }
        };

        // Only set defaults if data doesn't exist
        const existingData = await chrome.storage.local.get(Object.keys(defaultData));
        const dataToSet = {};
        
        for (const [key, value] of Object.entries(defaultData)) {
            if (!existingData[key]) {
                dataToSet[key] = value;
            }
        }

        if (Object.keys(dataToSet).length > 0) {
            await chrome.storage.local.set(dataToSet);
        }
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'startAutomation':
                    await this.startAutomation(message.data);
                    sendResponse({ success: true });
                    break;

                case 'stopAutomation':
                    await this.stopAutomation();
                    sendResponse({ success: true });
                    break;

                case 'updateProgress':
                    await this.updateProgress(message.data);
                    sendResponse({ success: true });
                    break;

                case 'logActivity':
                    await this.logActivity(message.data);
                    sendResponse({ success: true });
                    break;

                case 'checkDailyLimit':
                    const canSend = await this.checkDailyLimit();
                    sendResponse({ canSend });
                    break;

                case 'getSettings':
                    const settings = await this.getSettings();
                    sendResponse({ settings });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Background script error:', error);
            sendResponse({ error: error.message });
        }
    }

    async injectContentScript(tabId) {
        try {
            await chrome.scripting.executeScript({
                target: { tabId },
                files: ['content.js']
            });
        } catch (error) {
            console.error('Failed to inject content script:', error);
        }
    }

    async startAutomation(data) {
        this.isRunning = true;
        this.currentCampaign = data.campaign;
        
        // Update campaign status
        const { campaigns } = await chrome.storage.local.get(['campaigns']);
        const campaign = campaigns.find(c => c.id === data.campaign.id);
        if (campaign) {
            campaign.status = 'running';
            await chrome.storage.local.set({ campaigns });
        }

        console.log('Automation started for campaign:', data.campaign.name);
    }

    async stopAutomation() {
        this.isRunning = false;
        
        if (this.currentCampaign) {
            // Update campaign status
            const { campaigns } = await chrome.storage.local.get(['campaigns']);
            const campaign = campaigns.find(c => c.id === this.currentCampaign.id);
            if (campaign) {
                campaign.status = 'paused';
                await chrome.storage.local.set({ campaigns });
            }
        }

        this.currentCampaign = null;
        console.log('Automation stopped');
    }

    async updateProgress(data) {
        if (!this.currentCampaign) return;

        // Update campaign progress
        const { campaigns } = await chrome.storage.local.get(['campaigns']);
        const campaign = campaigns.find(c => c.id === this.currentCampaign.id);
        
        if (campaign) {
            campaign.profilesFound = data.profilesFound || campaign.profilesFound;
            campaign.messagesSent = data.messagesSent || campaign.messagesSent;
            campaign.connectionsAccepted = data.connectionsAccepted || campaign.connectionsAccepted;
            campaign.lastActivity = new Date().toISOString();
            
            await chrome.storage.local.set({ campaigns });
        }
    }

    async logActivity(data) {
        const { analytics } = await chrome.storage.local.get(['analytics']);
        
        // Update analytics
        if (data.type === 'message_sent') {
            analytics.totalSent++;
            analytics.todaySent++;
        } else if (data.type === 'connection_accepted') {
            analytics.totalAccepted++;
        }

        analytics.lastActivity = new Date().toISOString();
        await chrome.storage.local.set({ analytics });

        // Log to console for debugging
        console.log('Activity logged:', data);
    }

    async checkDailyLimit() {
        const { settings, analytics } = await chrome.storage.local.get(['settings', 'analytics']);
        
        // Reset daily count if it's a new day
        const today = new Date().toDateString();
        if (analytics.lastResetDate !== today) {
            analytics.todaySent = 0;
            analytics.lastResetDate = today;
            await chrome.storage.local.set({ analytics });
        }

        return analytics.todaySent < settings.dailyLimit;
    }

    async getSettings() {
        const { settings } = await chrome.storage.local.get(['settings']);
        return settings;
    }

    setupDailyReset() {
        // Calculate milliseconds until next midnight
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        const msUntilMidnight = tomorrow.getTime() - now.getTime();

        // Set timeout for midnight reset
        setTimeout(() => {
            this.resetDailyLimits();
            // Set interval for daily resets
            setInterval(() => {
                this.resetDailyLimits();
            }, 24 * 60 * 60 * 1000); // 24 hours
        }, msUntilMidnight);
    }

    async resetDailyLimits() {
        const { analytics } = await chrome.storage.local.get(['analytics']);
        analytics.todaySent = 0;
        analytics.lastResetDate = new Date().toDateString();
        await chrome.storage.local.set({ analytics });
        
        console.log('Daily limits reset');
    }

    // Utility function to generate random delay
    getRandomDelay(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // Utility function to personalize message template
    personalizeMessage(template, profile) {
        let message = template.content;
        
        // Replace placeholders with profile data
        message = message.replace(/{firstName}/g, profile.firstName || profile.name.split(' ')[0]);
        message = message.replace(/{lastName}/g, profile.lastName || profile.name.split(' ').slice(1).join(' '));
        message = message.replace(/{company}/g, profile.company || '');
        message = message.replace(/{title}/g, profile.title || '');
        message = message.replace(/{industry}/g, profile.industry || '');
        message = message.replace(/{location}/g, profile.location || '');
        
        return message;
    }

    // Function to validate LinkedIn profile URL
    isValidLinkedInProfile(url) {
        const linkedinProfileRegex = /^https:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9\-]+\/?$/;
        return linkedinProfileRegex.test(url);
    }

    // Function to extract profile ID from LinkedIn URL
    extractProfileId(url) {
        const match = url.match(/\/in\/([a-zA-Z0-9\-]+)/);
        return match ? match[1] : null;
    }

    // Function to check if profile was already contacted
    async isProfileContacted(profileId) {
        const { contactedProfiles } = await chrome.storage.local.get(['contactedProfiles']);
        return (contactedProfiles || []).includes(profileId);
    }

    // Function to mark profile as contacted
    async markProfileContacted(profileId) {
        const { contactedProfiles } = await chrome.storage.local.get(['contactedProfiles']);
        const contacted = contactedProfiles || [];
        
        if (!contacted.includes(profileId)) {
            contacted.push(profileId);
            await chrome.storage.local.set({ contactedProfiles: contacted });
        }
    }
}

// Initialize background script
const linkedinAutomation = new LinkedInAutomationBackground();
