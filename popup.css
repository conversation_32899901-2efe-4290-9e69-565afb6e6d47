* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    width: 400px;
    height: 600px;
    background: #f8f9fa;
    color: #333;
}

.container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

header {
    background: linear-gradient(135deg, #0077b5, #005885);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    font-size: 18px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4caf50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.tab-nav {
    display: flex;
    background: white;
    border-bottom: 1px solid #e1e5e9;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    color: #666;
    transition: all 0.2s;
    white-space: nowrap;
}

.tab-btn:hover {
    background: #f8f9fa;
    color: #0077b5;
}

.tab-btn.active {
    color: #0077b5;
    border-bottom: 2px solid #0077b5;
    background: white;
}

.tab-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: none;
}

.tab-content.active {
    display: block;
}

.section {
    margin-bottom: 24px;
}

.section h2 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #0077b5;
    box-shadow: 0 0 0 2px rgba(0, 119, 181, 0.1);
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    display: inline-block;
    text-decoration: none;
}

.btn-primary {
    background: #0077b5;
    color: white;
}

.btn-primary:hover {
    background: #005885;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
}

.campaigns-list,
.profiles-list,
.templates-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    background: white;
}

.campaign-item,
.profile-item,
.template-item {
    padding: 12px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.campaign-item:last-child,
.profile-item:last-child,
.template-item:last-child {
    border-bottom: none;
}

.campaign-info h3,
.profile-info h3,
.template-info h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
}

.campaign-info p,
.profile-info p,
.template-info p {
    font-size: 12px;
    color: #666;
}

.discovery-controls,
.template-controls,
.send-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.profile-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    align-items: center;
}

.profile-filters input {
    flex: 1;
}

.progress-container {
    margin: 16px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e1e5e9;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0077b5, #00a0dc);
    width: 0%;
    transition: width 0.3s ease;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.stat-card {
    background: white;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    text-align: center;
}

.stat-card h3 {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #0077b5;
}

.profile-item input[type="checkbox"] {
    margin-right: 8px;
}

.empty-state {
    text-align: center;
    padding: 32px 16px;
    color: #666;
    font-size: 14px;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;
}

.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #e1e5e9;
    border-top: 2px solid #0077b5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
