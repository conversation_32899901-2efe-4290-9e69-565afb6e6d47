// Popup script for LinkedIn Auto Connect & Message extension

class LinkedInAutomationPopup {
    constructor() {
        this.currentTab = 'campaigns';
        this.campaigns = [];
        this.profiles = [];
        this.templates = [];
        this.settings = {
            dailyLimit: 20,
            delayMin: 30,
            delayMax: 60,
            autoConnect: true,
            autoMessage: false
        };
        
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.renderCampaigns();
        this.renderProfiles();
        this.renderTemplates();
        this.renderAnalytics();
        this.loadSettings();
    }

    async loadData() {
        try {
            const result = await chrome.storage.local.get([
                'campaigns', 'profiles', 'templates', 'settings', 'analytics'
            ]);
            
            this.campaigns = result.campaigns || [];
            this.profiles = result.profiles || [];
            this.templates = result.templates || this.getDefaultTemplates();
            this.settings = { ...this.settings, ...result.settings };
            this.analytics = result.analytics || this.getDefaultAnalytics();
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Campaign form
        document.getElementById('campaignForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createCampaign();
        });

        // Profile discovery
        document.getElementById('startDiscovery').addEventListener('click', () => {
            this.startProfileDiscovery();
        });

        document.getElementById('stopDiscovery').addEventListener('click', () => {
            this.stopProfileDiscovery();
        });

        // Profile selection
        document.getElementById('selectAll').addEventListener('click', () => {
            this.selectAllProfiles(true);
        });

        document.getElementById('deselectAll').addEventListener('click', () => {
            this.selectAllProfiles(false);
        });

        // Message templates
        document.getElementById('addTemplate').addEventListener('click', () => {
            this.addMessageTemplate();
        });

        document.getElementById('generateAI').addEventListener('click', () => {
            this.generateAITemplate();
        });

        // Messaging
        document.getElementById('startMessaging').addEventListener('click', () => {
            this.startMessaging();
        });

        document.getElementById('stopMessaging').addEventListener('click', () => {
            this.stopMessaging();
        });

        // Settings
        document.getElementById('saveSettings').addEventListener('click', () => {
            this.saveSettings();
        });

        // Profile search
        document.getElementById('profileSearch').addEventListener('input', (e) => {
            this.filterProfiles(e.target.value);
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
    }

    async createCampaign() {
        const name = document.getElementById('campaignName').value;
        const sourceMethod = document.getElementById('sourceMethod').value;
        const targetCount = parseInt(document.getElementById('targetCount').value);

        if (!name || !sourceMethod) {
            alert('Please fill in all required fields');
            return;
        }

        const campaign = {
            id: Date.now().toString(),
            name,
            sourceMethod,
            targetCount,
            status: 'created',
            createdAt: new Date().toISOString(),
            profilesFound: 0,
            messagesSent: 0,
            connectionsAccepted: 0
        };

        this.campaigns.push(campaign);
        await this.saveData();
        this.renderCampaigns();

        // Reset form
        document.getElementById('campaignForm').reset();
        
        // Switch to profiles tab to start discovery
        this.switchTab('profiles');
    }

    async startProfileDiscovery() {
        const activeCampaign = this.campaigns.find(c => c.status === 'created' || c.status === 'discovering');
        if (!activeCampaign) {
            alert('Please create a campaign first');
            return;
        }

        activeCampaign.status = 'discovering';
        await this.saveData();

        // Send message to content script to start discovery
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        if (!tab.url.includes('linkedin.com')) {
            alert('Please navigate to LinkedIn first');
            return;
        }

        chrome.tabs.sendMessage(tab.id, {
            action: 'startDiscovery',
            method: activeCampaign.sourceMethod,
            targetCount: activeCampaign.targetCount
        });

        this.showDiscoveryProgress();
    }

    async stopProfileDiscovery() {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        chrome.tabs.sendMessage(tab.id, { action: 'stopDiscovery' });
        this.hideDiscoveryProgress();
    }

    showDiscoveryProgress() {
        document.getElementById('discoveryProgress').style.display = 'block';
        document.getElementById('startDiscovery').disabled = true;
    }

    hideDiscoveryProgress() {
        document.getElementById('discoveryProgress').style.display = 'none';
        document.getElementById('startDiscovery').disabled = false;
    }

    selectAllProfiles(select) {
        this.profiles.forEach(profile => {
            profile.selected = select;
        });
        this.renderProfiles();
        this.saveData();
    }

    filterProfiles(searchTerm) {
        const profileItems = document.querySelectorAll('.profile-item');
        profileItems.forEach(item => {
            const name = item.querySelector('.profile-info h3').textContent.toLowerCase();
            const title = item.querySelector('.profile-info p').textContent.toLowerCase();
            const matches = name.includes(searchTerm.toLowerCase()) || 
                          title.includes(searchTerm.toLowerCase());
            item.style.display = matches ? 'flex' : 'none';
        });
    }

    addMessageTemplate() {
        const template = prompt('Enter message template (use {firstName} for personalization):');
        if (template) {
            this.templates.push({
                id: Date.now().toString(),
                content: template,
                name: `Template ${this.templates.length + 1}`,
                createdAt: new Date().toISOString()
            });
            this.saveData();
            this.renderTemplates();
        }
    }

    generateAITemplate() {
        // Placeholder for AI template generation
        const aiTemplates = [
            "Hi {firstName}, I noticed your experience in {industry}. I'd love to connect and share insights about {topic}.",
            "Hello {firstName}, your background in {field} caught my attention. Would love to connect and learn from your expertise.",
            "Hi {firstName}, I'm impressed by your work at {company}. Let's connect and explore potential synergies."
        ];
        
        const randomTemplate = aiTemplates[Math.floor(Math.random() * aiTemplates.length)];
        this.templates.push({
            id: Date.now().toString(),
            content: randomTemplate,
            name: 'AI Generated Template',
            createdAt: new Date().toISOString()
        });
        this.saveData();
        this.renderTemplates();
    }

    async startMessaging() {
        const selectedProfiles = this.profiles.filter(p => p.selected);
        const activeTemplate = this.templates.find(t => t.active) || this.templates[0];
        
        if (selectedProfiles.length === 0) {
            alert('Please select profiles to message');
            return;
        }

        if (!activeTemplate) {
            alert('Please create a message template first');
            return;
        }

        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        chrome.tabs.sendMessage(tab.id, {
            action: 'startMessaging',
            profiles: selectedProfiles,
            template: activeTemplate,
            settings: this.settings
        });

        this.showMessagingProgress();
    }

    async stopMessaging() {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        chrome.tabs.sendMessage(tab.id, { action: 'stopMessaging' });
        this.hideMessagingProgress();
    }

    showMessagingProgress() {
        document.getElementById('messagingProgress').style.display = 'block';
        document.getElementById('startMessaging').disabled = true;
    }

    hideMessagingProgress() {
        document.getElementById('messagingProgress').style.display = 'none';
        document.getElementById('startMessaging').disabled = false;
    }

    renderCampaigns() {
        const container = document.getElementById('campaignsList');
        
        if (this.campaigns.length === 0) {
            container.innerHTML = '<div class="empty-state">No campaigns created yet</div>';
            return;
        }

        container.innerHTML = this.campaigns.map(campaign => `
            <div class="campaign-item">
                <div class="campaign-info">
                    <h3>${campaign.name}</h3>
                    <p>${campaign.sourceMethod} • ${campaign.profilesFound} profiles • ${campaign.messagesSent} sent</p>
                </div>
                <div class="campaign-actions">
                    <span class="status ${campaign.status}">${campaign.status}</span>
                </div>
            </div>
        `).join('');
    }

    renderProfiles() {
        const container = document.getElementById('profilesList');
        
        if (this.profiles.length === 0) {
            container.innerHTML = '<div class="empty-state">No profiles discovered yet</div>';
            return;
        }

        container.innerHTML = this.profiles.map(profile => `
            <div class="profile-item">
                <input type="checkbox" ${profile.selected ? 'checked' : ''} 
                       onchange="popup.toggleProfile('${profile.id}')">
                <div class="profile-info">
                    <h3>${profile.name}</h3>
                    <p>${profile.title} at ${profile.company}</p>
                </div>
            </div>
        `).join('');
    }

    toggleProfile(profileId) {
        const profile = this.profiles.find(p => p.id === profileId);
        if (profile) {
            profile.selected = !profile.selected;
            this.saveData();
        }
    }

    renderTemplates() {
        const container = document.getElementById('messageTemplates');
        
        if (this.templates.length === 0) {
            container.innerHTML = '<div class="empty-state">No message templates created yet</div>';
            return;
        }

        container.innerHTML = this.templates.map(template => `
            <div class="template-item">
                <div class="template-info">
                    <h3>${template.name}</h3>
                    <p>${template.content.substring(0, 50)}...</p>
                </div>
                <div class="template-actions">
                    <button class="btn btn-small" onclick="popup.setActiveTemplate('${template.id}')">
                        ${template.active ? 'Active' : 'Use'}
                    </button>
                </div>
            </div>
        `).join('');
    }

    setActiveTemplate(templateId) {
        this.templates.forEach(t => t.active = false);
        const template = this.templates.find(t => t.id === templateId);
        if (template) {
            template.active = true;
            this.saveData();
            this.renderTemplates();
        }
    }

    renderAnalytics() {
        document.getElementById('totalSent').textContent = this.analytics.totalSent || 0;
        document.getElementById('totalAccepted').textContent = this.analytics.totalAccepted || 0;
        document.getElementById('responseRate').textContent = 
            this.analytics.totalSent > 0 ? 
            Math.round((this.analytics.totalAccepted / this.analytics.totalSent) * 100) + '%' : '0%';
        document.getElementById('todaySent').textContent = this.analytics.todaySent || 0;
    }

    loadSettings() {
        document.getElementById('dailyLimit').value = this.settings.dailyLimit;
        document.getElementById('delayMin').value = this.settings.delayMin;
        document.getElementById('delayMax').value = this.settings.delayMax;
        document.getElementById('autoConnect').checked = this.settings.autoConnect;
        document.getElementById('autoMessage').checked = this.settings.autoMessage;
    }

    async saveSettings() {
        this.settings = {
            dailyLimit: parseInt(document.getElementById('dailyLimit').value),
            delayMin: parseInt(document.getElementById('delayMin').value),
            delayMax: parseInt(document.getElementById('delayMax').value),
            autoConnect: document.getElementById('autoConnect').checked,
            autoMessage: document.getElementById('autoMessage').checked
        };

        await this.saveData();
        alert('Settings saved successfully!');
    }

    async saveData() {
        await chrome.storage.local.set({
            campaigns: this.campaigns,
            profiles: this.profiles,
            templates: this.templates,
            settings: this.settings,
            analytics: this.analytics
        });
    }

    getDefaultTemplates() {
        return [
            {
                id: '1',
                name: 'Professional Introduction',
                content: 'Hi {firstName}, I came across your profile and was impressed by your experience in {industry}. I\'d love to connect and potentially collaborate.',
                active: true,
                createdAt: new Date().toISOString()
            }
        ];
    }

    getDefaultAnalytics() {
        return {
            totalSent: 0,
            totalAccepted: 0,
            todaySent: 0,
            lastResetDate: new Date().toDateString()
        };
    }
}

// Initialize popup when DOM is loaded
let popup;
document.addEventListener('DOMContentLoaded', () => {
    popup = new LinkedInAutomationPopup();
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'profilesFound') {
        popup.profiles = message.profiles;
        popup.renderProfiles();
        popup.saveData();
        
        // Update progress
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        progressFill.style.width = '100%';
        progressText.textContent = `${message.profiles.length} profiles found`;
    }
    
    if (message.action === 'messageSent') {
        popup.analytics.totalSent++;
        popup.analytics.todaySent++;
        popup.renderAnalytics();
        popup.saveData();
        
        // Update progress
        const progressFill = document.getElementById('messageProgressFill');
        const progressText = document.getElementById('messageProgressText');
        const progress = (message.sent / message.total) * 100;
        progressFill.style.width = progress + '%';
        progressText.textContent = `${message.sent} of ${message.total} messages sent`;
    }
});
