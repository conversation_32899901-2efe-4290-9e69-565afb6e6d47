# Installation Guide - LinkedIn Auto Connect & Message Extension

## Quick Installation Steps

### 1. Prepare the Extension Files
Make sure you have all the required files in your project directory:
```
D:\Autviz\AI-testing\
├── manifest.json
├── popup.html
├── popup.css
├── popup.js
├── background.js
├── content.js
├── content.css
├── icons/
│   └── icon.svg
├── README.md
└── INSTALLATION.md
```

### 2. Load the Extension in Chrome/Edge

1. **Open Chrome or Edge browser**

2. **Navigate to Extensions page**:
   - Chrome: Go to `chrome://extensions/`
   - Edge: Go to `edge://extensions/`

3. **Enable Developer Mode**:
   - Toggle the "Developer mode" switch in the top-right corner

4. **Load the Extension**:
   - Click "Load unpacked" button
   - Navigate to and select the folder: `D:\Autviz\AI-testing`
   - Click "Select Folder"

5. **Verify Installation**:
   - The extension should appear in your extensions list
   - You should see "LinkedIn Auto Connect & Message" with version 1.0.0
   - The extension icon will appear in your browser toolbar

### 3. Grant Permissions

When you first use the extension on LinkedIn:
1. The browser will ask for permissions to access LinkedIn
2. Click "Allow" to grant the necessary permissions
3. The extension needs these permissions to:
   - Read LinkedIn page content
   - Store campaign data locally
   - Inject automation scripts

### 4. Test the Extension

1. **Open LinkedIn**:
   - Navigate to `https://linkedin.com`
   - Log into your LinkedIn account

2. **Open the Extension**:
   - Click the extension icon in your browser toolbar
   - The popup should open showing the campaign interface

3. **Create a Test Campaign**:
   - Go to the "Campaigns" tab
   - Fill in a campaign name (e.g., "Test Campaign")
   - Select a discovery method (e.g., "LinkedIn Search")
   - Set target count to 5-10 for testing
   - Click "Create Campaign"

4. **Test Profile Discovery**:
   - Navigate to LinkedIn People Search
   - Go back to the extension and click the "Profiles" tab
   - Click "Start Profile Discovery"
   - You should see the automation UI appear on the LinkedIn page

## Troubleshooting

### Extension Won't Load
- **Error**: "Could not load manifest"
  - **Solution**: Make sure all files are in the correct directory
  - Check that manifest.json is valid JSON

- **Error**: "File not found"
  - **Solution**: Verify all referenced files exist in the directory

### Extension Loads but Doesn't Work
- **Issue**: Popup doesn't open
  - **Solution**: Right-click the extension icon and select "Inspect popup" to check for errors

- **Issue**: No automation UI on LinkedIn
  - **Solution**: Refresh the LinkedIn page and check browser console for errors

### Permission Issues
- **Issue**: "Cannot access LinkedIn"
  - **Solution**: Make sure you granted permissions when prompted
  - Go to extension settings and verify LinkedIn access is allowed

### Content Script Issues
- **Issue**: Automation doesn't start
  - **Solution**: Check browser console (F12) for JavaScript errors
  - Make sure you're on a supported LinkedIn page

## Development Mode Features

While in developer mode, you can:

1. **View Console Logs**:
   - Right-click extension icon → "Inspect popup" for popup logs
   - F12 on LinkedIn pages for content script logs

2. **Reload Extension**:
   - Click the reload button on the extension card
   - Useful when making code changes

3. **View Storage Data**:
   - Go to Application tab in DevTools
   - Check Local Storage for extension data

## Security Notes

- The extension only works on LinkedIn domains
- All data is stored locally in your browser
- No external servers are contacted
- You can remove the extension at any time to delete all data

## Next Steps

Once installed successfully:
1. Read the main README.md for detailed usage instructions
2. Start with small test campaigns (5-10 profiles)
3. Configure appropriate delays and limits in Settings
4. Monitor the Analytics tab for performance tracking

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify you're using a supported browser (Chrome 88+ or Edge 88+)
3. Make sure you're logged into LinkedIn
4. Try refreshing both the LinkedIn page and reloading the extension

## Uninstallation

To remove the extension:
1. Go to `chrome://extensions/` or `edge://extensions/`
2. Find "LinkedIn Auto Connect & Message"
3. Click "Remove"
4. Confirm removal

All locally stored data will be deleted when you remove the extension.
