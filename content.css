/* Content script styles for LinkedIn Auto Connect & Message extension */

#linkedin-automation-ui {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 999999 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
}

.automation-panel {
    background: white !important;
    border: 1px solid #e1e5e9 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    min-width: 280px !important;
    max-width: 350px !important;
    overflow: hidden !important;
}

.automation-header {
    background: linear-gradient(135deg, #0077b5, #005885) !important;
    color: white !important;
    padding: 12px 16px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    cursor: move !important;
}

.automation-header button {
    background: none !important;
    border: none !important;
    color: white !important;
    font-size: 18px !important;
    cursor: pointer !important;
    padding: 0 !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 3px !important;
    transition: background-color 0.2s !important;
}

.automation-header button:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

.automation-content {
    padding: 16px !important;
    background: white !important;
}

.status-display {
    font-size: 12px !important;
    color: #666 !important;
    margin-bottom: 8px !important;
    font-weight: 500 !important;
}

.progress-display {
    margin-top: 12px !important;
}

.progress-bar {
    width: 100% !important;
    height: 6px !important;
    background: #e1e5e9 !important;
    border-radius: 3px !important;
    overflow: hidden !important;
    margin-bottom: 8px !important;
}

.progress-fill {
    height: 100% !important;
    background: linear-gradient(90deg, #0077b5, #00a0dc) !important;
    width: 0% !important;
    transition: width 0.3s ease !important;
    border-radius: 3px !important;
}

.progress-display span {
    font-size: 11px !important;
    color: #666 !important;
    font-weight: 500 !important;
}

/* Animation for status updates */
@keyframes statusPulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.status-display.active {
    animation: statusPulse 2s infinite !important;
    color: #0077b5 !important;
}

/* Draggable functionality */
.automation-panel.dragging {
    opacity: 0.8 !important;
    transform: rotate(2deg) !important;
    transition: none !important;
}

/* Success/Error states */
.status-display.success {
    color: #4caf50 !important;
}

.status-display.error {
    color: #f44336 !important;
}

.status-display.warning {
    color: #ff9800 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #linkedin-automation-ui {
        top: 10px !important;
        right: 10px !important;
    }
    
    .automation-panel {
        min-width: 250px !important;
        max-width: 280px !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .automation-panel {
        border: 2px solid #000 !important;
    }
    
    .automation-header {
        background: #000 !important;
        color: #fff !important;
    }
    
    .progress-fill {
        background: #000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .progress-fill,
    .automation-header button,
    .automation-panel.dragging {
        transition: none !important;
    }
    
    .status-display.active {
        animation: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .automation-panel {
        background: #1e1e1e !important;
        border-color: #333 !important;
        color: #fff !important;
    }
    
    .automation-content {
        background: #1e1e1e !important;
    }
    
    .status-display {
        color: #ccc !important;
    }
    
    .progress-bar {
        background: #333 !important;
    }
}

/* Ensure the UI doesn't interfere with LinkedIn's elements */
#linkedin-automation-ui * {
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
}

#linkedin-automation-ui *:not(.automation-header):not(.automation-content):not(.status-display):not(.progress-display) {
    margin: inherit !important;
    padding: inherit !important;
}

/* Override any conflicting LinkedIn styles */
#linkedin-automation-ui .automation-panel {
    position: relative !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
}

/* Tooltip styles for additional information */
.automation-tooltip {
    position: absolute !important;
    bottom: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 6px 10px !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    pointer-events: none !important;
    transition: opacity 0.2s !important;
    margin-bottom: 5px !important;
}

.automation-tooltip::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    border: 4px solid transparent !important;
    border-top-color: rgba(0, 0, 0, 0.8) !important;
}

.automation-header:hover .automation-tooltip {
    opacity: 1 !important;
}

/* Loading spinner for status */
.status-loading::after {
    content: '' !important;
    display: inline-block !important;
    width: 12px !important;
    height: 12px !important;
    border: 2px solid #e1e5e9 !important;
    border-top: 2px solid #0077b5 !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
    margin-left: 8px !important;
    vertical-align: middle !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Ensure proper stacking context */
#linkedin-automation-ui {
    isolation: isolate !important;
}
