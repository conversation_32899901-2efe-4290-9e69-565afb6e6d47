# LinkedIn Auto Connect & Message Extension

A powerful browser extension for automating LinkedIn connections and messaging with intelligent campaign management.

## Features

### 🎯 Campaign Management
- Create and manage multiple outreach campaigns
- Track campaign performance and analytics
- Organize campaigns by different discovery methods

### 🔍 Profile Discovery Methods
- **LinkedIn Search**: Extract profiles from LinkedIn search results with advanced filtering
- **Sales Navigator**: Discover profiles from LinkedIn Sales Navigator searches
- **Connection Search**: Find profiles from your existing LinkedIn connections

### 💬 Intelligent Messaging
- Customizable message templates with personalization variables
- AI-powered message generation suggestions
- Support for both connection requests and direct messages
- Smart personalization using profile data

### 🛡️ Safety & Compliance
- Daily message limits to avoid LinkedIn restrictions
- Random delays between actions to mimic human behavior
- Rate limiting and safety controls
- Compliance with LinkedIn's terms of service

### 📊 Analytics & Tracking
- Real-time campaign progress tracking
- Message delivery and acceptance rates
- Daily activity monitoring
- Comprehensive analytics dashboard

## Installation

1. Download or clone this repository
2. Open Chrome/Edge and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension icon will appear in your browser toolbar

## Usage

### Step 1: Create a Campaign
1. Click the extension icon to open the popup
2. Go to the "Campaigns" tab
3. Fill in campaign details:
   - Campaign name
   - Profile discovery method
   - Target profile count
4. Click "Create Campaign"

### Step 2: Discover Profiles
1. Navigate to LinkedIn in your browser
2. Go to the appropriate page based on your chosen method:
   - **Search**: LinkedIn People Search results
   - **Sales Navigator**: Sales Navigator People Search
   - **Connections**: Your LinkedIn connections page
3. Switch to the "Profiles" tab in the extension
4. Click "Start Profile Discovery"
5. The extension will automatically scroll and collect profiles

### Step 3: Select Profiles
1. Review the discovered profiles in the "Profiles" tab
2. Use the search filter to find specific profiles
3. Select/deselect profiles using checkboxes
4. Use "Select All" or "Deselect All" for bulk actions

### Step 4: Configure Messages
1. Go to the "Messages" tab
2. Create custom message templates or use AI generation
3. Use personalization variables like `{firstName}`, `{company}`, `{title}`
4. Select the active template for your campaign

### Step 5: Start Automation
1. Configure settings in the "Settings" tab:
   - Daily message limit
   - Delay between messages
   - Connection vs. direct message preferences
2. Return to the "Messages" tab
3. Click "Start Messaging"
4. Monitor progress in real-time

## Personalization Variables

Use these variables in your message templates for automatic personalization:

- `{firstName}` - Contact's first name
- `{lastName}` - Contact's last name
- `{name}` - Full name
- `{company}` - Company name
- `{title}` - Job title
- `{location}` - Location
- `{industry}` - Detected industry
- `{field}` - Detected field/specialization

## Safety Features

### Rate Limiting
- Configurable delays between messages (30-60 seconds default)
- Daily message limits (20 messages default)
- Random timing to avoid detection

### Compliance
- Respects LinkedIn's automation policies
- Includes safety warnings and best practices
- Automatic daily limit resets

### Data Privacy
- All data stored locally in browser
- No external servers or data transmission
- User has full control over stored data

## Settings Configuration

### Automation Settings
- **Daily Limit**: Maximum messages per day (1-100)
- **Min/Max Delay**: Random delay range between actions
- **Auto Connect**: Send connection requests
- **Auto Message**: Send messages after connections

### Message Templates
- Create unlimited custom templates
- AI-powered template suggestions
- Template performance tracking

## Analytics Dashboard

Track your campaign performance with:
- Total messages sent
- Connection acceptance rate
- Response rate tracking
- Daily activity summaries

## Troubleshooting

### Common Issues

**Extension not working on LinkedIn**
- Ensure you're logged into LinkedIn
- Refresh the LinkedIn page
- Check that the extension is enabled

**Profile discovery not finding profiles**
- Make sure you're on the correct LinkedIn page
- Try scrolling manually first
- Check your search filters

**Messages not sending**
- Verify daily limits haven't been reached
- Check LinkedIn connection status
- Ensure message templates are configured

**Daily limit reached**
- Limits reset at midnight local time
- Adjust limits in Settings if needed
- Monitor usage in Analytics tab

### Best Practices

1. **Start Small**: Begin with low daily limits (10-20 messages)
2. **Personalize Messages**: Use profile data for better response rates
3. **Monitor Analytics**: Track performance and adjust strategy
4. **Respect Limits**: Don't exceed LinkedIn's recommended limits
5. **Quality over Quantity**: Focus on relevant, targeted connections

## Technical Details

### Browser Compatibility
- Chrome 88+
- Edge 88+
- Other Chromium-based browsers

### Permissions Required
- `activeTab`: Access to current LinkedIn tab
- `storage`: Local data storage
- `scripting`: Content script injection
- `tabs`: Tab management for automation

### Data Storage
All data is stored locally using Chrome's storage API:
- Campaign configurations
- Discovered profiles
- Message templates
- Analytics data
- User settings

## Support

For issues, questions, or feature requests:
1. Check the troubleshooting section above
2. Review LinkedIn's automation policies
3. Ensure you're using the latest version

## Disclaimer

This extension is for educational and productivity purposes. Users are responsible for complying with LinkedIn's Terms of Service and applicable laws. Use responsibly and respect others' privacy and preferences.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
