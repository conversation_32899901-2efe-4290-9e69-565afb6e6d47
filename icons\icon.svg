<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0077b5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#005885;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#003d5c" stroke-width="2"/>
  
  <!-- LinkedIn-style icon -->
  <g fill="white">
    <!-- Connection nodes -->
    <circle cx="35" cy="45" r="8"/>
    <circle cx="93" cy="45" r="8"/>
    <circle cx="64" cy="85" r="8"/>
    
    <!-- Connection lines -->
    <line x1="43" y1="45" x2="85" y2="45" stroke="white" stroke-width="3" stroke-linecap="round"/>
    <line x1="35" y1="53" x2="64" y2="77" stroke="white" stroke-width="3" stroke-linecap="round"/>
    <line x1="93" y1="53" x2="64" y2="77" stroke="white" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Automation symbol -->
    <g transform="translate(64, 64)">
      <circle cx="0" cy="0" r="3" fill="white"/>
      <path d="M-15,-15 L-10,-10 M15,-15 L10,-10 M-15,15 L-10,10 M15,15 L10,10" 
            stroke="white" stroke-width="2" stroke-linecap="round"/>
    </g>
  </g>
  
  <!-- Text -->
  <text x="64" y="110" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">AUTO</text>
</svg>
