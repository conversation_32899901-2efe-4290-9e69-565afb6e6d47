// Content script for LinkedIn Auto Connect & Message extension

class LinkedInAutomation {
    constructor() {
        this.isDiscovering = false;
        this.isMessaging = false;
        this.discoveredProfiles = [];
        this.currentProfileIndex = 0;
        this.settings = {};
        
        this.init();
    }

    async init() {
        // Load settings from storage
        await this.loadSettings();
        
        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });

        // Inject automation UI if not already present
        this.injectAutomationUI();
        
        console.log('LinkedIn Automation content script loaded');
    }

    async loadSettings() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
            this.settings = response.settings || {};
        } catch (error) {
            console.error('Failed to load settings:', error);
            this.settings = {
                dailyLimit: 20,
                delayMin: 30,
                delayMax: 60,
                autoConnect: true,
                autoMessage: false
            };
        }
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'startDiscovery':
                this.startProfileDiscovery(message.method, message.targetCount);
                sendResponse({ success: true });
                break;

            case 'stopDiscovery':
                this.stopProfileDiscovery();
                sendResponse({ success: true });
                break;

            case 'startMessaging':
                this.startMessaging(message.profiles, message.template, message.settings);
                sendResponse({ success: true });
                break;

            case 'stopMessaging':
                this.stopMessaging();
                sendResponse({ success: true });
                break;

            default:
                sendResponse({ error: 'Unknown action' });
        }
    }

    injectAutomationUI() {
        // Check if UI already exists
        if (document.getElementById('linkedin-automation-ui')) return;

        const ui = document.createElement('div');
        ui.id = 'linkedin-automation-ui';
        ui.innerHTML = `
            <div class="automation-panel">
                <div class="automation-header">
                    <span>LinkedIn Automation</span>
                    <button id="togglePanel">−</button>
                </div>
                <div class="automation-content">
                    <div class="status-display">
                        <span id="automationStatus">Ready</span>
                    </div>
                    <div class="progress-display" id="progressDisplay" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <span id="progressText">0/0</span>
                    </div>
                </div>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            #linkedin-automation-ui {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            
            .automation-panel {
                background: white;
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                min-width: 250px;
            }
            
            .automation-header {
                background: linear-gradient(135deg, #0077b5, #005885);
                color: white;
                padding: 12px 16px;
                border-radius: 8px 8px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
            }
            
            .automation-header button {
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .automation-content {
                padding: 16px;
            }
            
            .status-display {
                font-size: 12px;
                color: #666;
                margin-bottom: 8px;
            }
            
            .progress-display {
                margin-top: 12px;
            }
            
            .progress-bar {
                width: 100%;
                height: 6px;
                background: #e1e5e9;
                border-radius: 3px;
                overflow: hidden;
                margin-bottom: 8px;
            }
            
            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #0077b5, #00a0dc);
                width: 0%;
                transition: width 0.3s ease;
            }
            
            .progress-display span {
                font-size: 11px;
                color: #666;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(ui);

        // Add toggle functionality
        document.getElementById('togglePanel').addEventListener('click', () => {
            const content = document.querySelector('.automation-content');
            const button = document.getElementById('togglePanel');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                button.textContent = '−';
            } else {
                content.style.display = 'none';
                button.textContent = '+';
            }
        });
    }

    updateStatus(status) {
        const statusElement = document.getElementById('automationStatus');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    updateProgress(current, total) {
        const progressDisplay = document.getElementById('progressDisplay');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        if (progressDisplay && progressFill && progressText) {
            progressDisplay.style.display = 'block';
            const percentage = total > 0 ? (current / total) * 100 : 0;
            progressFill.style.width = percentage + '%';
            progressText.textContent = `${current}/${total}`;
        }
    }

    hideProgress() {
        const progressDisplay = document.getElementById('progressDisplay');
        if (progressDisplay) {
            progressDisplay.style.display = 'none';
        }
    }

    async startProfileDiscovery(method, targetCount) {
        if (this.isDiscovering) return;
        
        this.isDiscovering = true;
        this.discoveredProfiles = [];
        this.updateStatus('Discovering profiles...');
        
        try {
            switch (method) {
                case 'search':
                    await this.discoverFromSearch(targetCount);
                    break;
                case 'sales-navigator':
                    await this.discoverFromSalesNavigator(targetCount);
                    break;
                case 'connections':
                    await this.discoverFromConnections(targetCount);
                    break;
                default:
                    throw new Error('Unknown discovery method');
            }
            
            // Send discovered profiles to popup
            chrome.runtime.sendMessage({
                action: 'profilesFound',
                profiles: this.discoveredProfiles
            });
            
            this.updateStatus(`Found ${this.discoveredProfiles.length} profiles`);
            
        } catch (error) {
            console.error('Profile discovery error:', error);
            this.updateStatus('Discovery failed');
        } finally {
            this.isDiscovering = false;
            this.hideProgress();
        }
    }

    stopProfileDiscovery() {
        this.isDiscovering = false;
        this.updateStatus('Discovery stopped');
        this.hideProgress();
    }

    async discoverFromSearch(targetCount) {
        // Check if we're on a LinkedIn search results page
        if (!window.location.href.includes('/search/people/')) {
            throw new Error('Please navigate to LinkedIn People Search first');
        }

        let profileCount = 0;
        let scrollAttempts = 0;
        const maxScrollAttempts = 10;

        while (profileCount < targetCount && scrollAttempts < maxScrollAttempts && this.isDiscovering) {
            // Find profile cards on the page
            const profileCards = document.querySelectorAll('[data-view-name="search-entity-result-universal-template"]');

            for (const card of profileCards) {
                if (profileCount >= targetCount || !this.isDiscovering) break;

                const profile = this.extractProfileFromSearchCard(card);
                if (profile && !this.discoveredProfiles.find(p => p.url === profile.url)) {
                    this.discoveredProfiles.push(profile);
                    profileCount++;
                    this.updateProgress(profileCount, targetCount);
                }
            }

            // Scroll to load more results
            if (profileCount < targetCount) {
                window.scrollTo(0, document.body.scrollHeight);
                await this.delay(2000); // Wait for new content to load
                scrollAttempts++;
            }
        }
    }

    async discoverFromSalesNavigator(targetCount) {
        // Check if we're on Sales Navigator
        if (!window.location.href.includes('sales/search/people')) {
            throw new Error('Please navigate to LinkedIn Sales Navigator People Search first');
        }

        let profileCount = 0;
        let scrollAttempts = 0;
        const maxScrollAttempts = 10;

        while (profileCount < targetCount && scrollAttempts < maxScrollAttempts && this.isDiscovering) {
            // Find profile cards in Sales Navigator
            const profileCards = document.querySelectorAll('.artdeco-entity-lockup');

            for (const card of profileCards) {
                if (profileCount >= targetCount || !this.isDiscovering) break;

                const profile = this.extractProfileFromSalesNavCard(card);
                if (profile && !this.discoveredProfiles.find(p => p.url === profile.url)) {
                    this.discoveredProfiles.push(profile);
                    profileCount++;
                    this.updateProgress(profileCount, targetCount);
                }
            }

            // Scroll to load more results
            if (profileCount < targetCount) {
                window.scrollTo(0, document.body.scrollHeight);
                await this.delay(2000);
                scrollAttempts++;
            }
        }
    }

    async discoverFromConnections(targetCount) {
        // Navigate to connections page if not already there
        if (!window.location.href.includes('/mynetwork/invite-connect/connections/')) {
            window.location.href = 'https://www.linkedin.com/mynetwork/invite-connect/connections/';
            await this.delay(3000);
        }

        let profileCount = 0;
        let scrollAttempts = 0;
        const maxScrollAttempts = 10;

        while (profileCount < targetCount && scrollAttempts < maxScrollAttempts && this.isDiscovering) {
            // Find connection cards
            const connectionCards = document.querySelectorAll('.mn-connection-card');

            for (const card of connectionCards) {
                if (profileCount >= targetCount || !this.isDiscovering) break;

                const profile = this.extractProfileFromConnectionCard(card);
                if (profile && !this.discoveredProfiles.find(p => p.url === profile.url)) {
                    this.discoveredProfiles.push(profile);
                    profileCount++;
                    this.updateProgress(profileCount, targetCount);
                }
            }

            // Scroll to load more connections
            if (profileCount < targetCount) {
                window.scrollTo(0, document.body.scrollHeight);
                await this.delay(2000);
                scrollAttempts++;
            }
        }
    }

    extractProfileFromSearchCard(card) {
        try {
            const nameElement = card.querySelector('.entity-result__title-text a');
            const titleElement = card.querySelector('.entity-result__primary-subtitle');
            const companyElement = card.querySelector('.entity-result__secondary-subtitle');
            const locationElement = card.querySelector('.entity-result__summary .t-12');
            const imageElement = card.querySelector('.presence-entity__image');

            if (!nameElement) return null;

            const name = nameElement.textContent.trim();
            const url = nameElement.href;
            const title = titleElement ? titleElement.textContent.trim() : '';
            const company = companyElement ? companyElement.textContent.trim() : '';
            const location = locationElement ? locationElement.textContent.trim() : '';
            const image = imageElement ? imageElement.src : '';

            return {
                id: this.generateProfileId(url),
                name,
                url,
                title,
                company,
                location,
                image,
                firstName: name.split(' ')[0],
                lastName: name.split(' ').slice(1).join(' '),
                selected: false,
                discoveredAt: new Date().toISOString(),
                source: 'search'
            };
        } catch (error) {
            console.error('Error extracting profile from search card:', error);
            return null;
        }
    }

    extractProfileFromSalesNavCard(card) {
        try {
            const nameElement = card.querySelector('.artdeco-entity-lockup__title a');
            const titleElement = card.querySelector('.artdeco-entity-lockup__subtitle');
            const companyElement = card.querySelector('.artdeco-entity-lockup__caption');
            const imageElement = card.querySelector('.presence-entity__image');

            if (!nameElement) return null;

            const name = nameElement.textContent.trim();
            const url = nameElement.href;
            const title = titleElement ? titleElement.textContent.trim() : '';
            const company = companyElement ? companyElement.textContent.trim() : '';
            const image = imageElement ? imageElement.src : '';

            return {
                id: this.generateProfileId(url),
                name,
                url,
                title,
                company,
                location: '',
                image,
                firstName: name.split(' ')[0],
                lastName: name.split(' ').slice(1).join(' '),
                selected: false,
                discoveredAt: new Date().toISOString(),
                source: 'sales-navigator'
            };
        } catch (error) {
            console.error('Error extracting profile from Sales Navigator card:', error);
            return null;
        }
    }

    extractProfileFromConnectionCard(card) {
        try {
            const nameElement = card.querySelector('.mn-connection-card__name');
            const titleElement = card.querySelector('.mn-connection-card__occupation');
            const linkElement = card.querySelector('.mn-connection-card__link');
            const imageElement = card.querySelector('.presence-entity__image');

            if (!nameElement || !linkElement) return null;

            const name = nameElement.textContent.trim();
            const url = linkElement.href;
            const title = titleElement ? titleElement.textContent.trim() : '';
            const image = imageElement ? imageElement.src : '';

            return {
                id: this.generateProfileId(url),
                name,
                url,
                title,
                company: '',
                location: '',
                image,
                firstName: name.split(' ')[0],
                lastName: name.split(' ').slice(1).join(' '),
                selected: false,
                discoveredAt: new Date().toISOString(),
                source: 'connections'
            };
        } catch (error) {
            console.error('Error extracting profile from connection card:', error);
            return null;
        }
    }

    generateProfileId(url) {
        // Extract profile ID from LinkedIn URL
        const match = url.match(/\/in\/([^\/\?]+)/);
        return match ? match[1] : Date.now().toString();
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async startMessaging(profiles, template, settings) {
        if (this.isMessaging) return;

        this.isMessaging = true;
        this.currentProfileIndex = 0;
        this.settings = { ...this.settings, ...settings };

        const selectedProfiles = profiles.filter(p => p.selected);
        this.updateStatus(`Starting to message ${selectedProfiles.length} profiles...`);

        try {
            for (let i = 0; i < selectedProfiles.length && this.isMessaging; i++) {
                const profile = selectedProfiles[i];
                this.currentProfileIndex = i;
                this.updateProgress(i + 1, selectedProfiles.length);

                // Check daily limit
                const canSend = await chrome.runtime.sendMessage({ action: 'checkDailyLimit' });
                if (!canSend.canSend) {
                    this.updateStatus('Daily limit reached');
                    break;
                }

                await this.sendMessageToProfile(profile, template);

                // Random delay between messages
                const delay = this.getRandomDelay(this.settings.delayMin * 1000, this.settings.delayMax * 1000);
                await this.delay(delay);
            }

            this.updateStatus(`Messaging completed. Sent ${this.currentProfileIndex} messages.`);

        } catch (error) {
            console.error('Messaging error:', error);
            this.updateStatus('Messaging failed');
        } finally {
            this.isMessaging = false;
            this.hideProgress();
        }
    }

    stopMessaging() {
        this.isMessaging = false;
        this.updateStatus('Messaging stopped');
        this.hideProgress();
    }

    async sendMessageToProfile(profile, template) {
        try {
            // Navigate to profile page
            window.open(profile.url, '_blank');
            await this.delay(3000); // Wait for page to load

            // Switch to the new tab
            const tabs = await chrome.tabs.query({});
            const profileTab = tabs.find(tab => tab.url.includes(profile.id));

            if (profileTab) {
                await chrome.tabs.update(profileTab.id, { active: true });
                await this.delay(2000);

                // Send connection request or message
                if (this.settings.autoConnect) {
                    await this.sendConnectionRequest(profile, template);
                } else if (this.settings.autoMessage) {
                    await this.sendDirectMessage(profile, template);
                }

                // Log activity
                await chrome.runtime.sendMessage({
                    action: 'logActivity',
                    data: {
                        type: 'message_sent',
                        profileId: profile.id,
                        profileName: profile.name,
                        timestamp: new Date().toISOString()
                    }
                });

                // Send progress update to popup
                chrome.runtime.sendMessage({
                    action: 'messageSent',
                    sent: this.currentProfileIndex + 1,
                    total: this.profiles.filter(p => p.selected).length
                });

                // Close the profile tab
                await chrome.tabs.remove(profileTab.id);
            }

        } catch (error) {
            console.error('Error sending message to profile:', error);
        }
    }

    async sendConnectionRequest(profile, template) {
        try {
            // Look for Connect button
            const connectButton = document.querySelector('[data-control-name="connect"]') ||
                                document.querySelector('button[aria-label*="Connect"]') ||
                                document.querySelector('button:contains("Connect")');

            if (connectButton && connectButton.textContent.includes('Connect')) {
                connectButton.click();
                await this.delay(2000);

                // Look for "Add a note" button
                const addNoteButton = document.querySelector('[aria-label="Add a note"]') ||
                                    document.querySelector('button:contains("Add a note")');

                if (addNoteButton) {
                    addNoteButton.click();
                    await this.delay(1000);

                    // Find the message textarea
                    const messageTextarea = document.querySelector('#custom-message') ||
                                          document.querySelector('textarea[name="message"]') ||
                                          document.querySelector('textarea[aria-label*="message"]');

                    if (messageTextarea) {
                        const personalizedMessage = this.personalizeMessage(template.content, profile);
                        messageTextarea.value = personalizedMessage;
                        messageTextarea.dispatchEvent(new Event('input', { bubbles: true }));
                        await this.delay(1000);
                    }
                }

                // Send the connection request
                const sendButton = document.querySelector('[aria-label="Send invitation"]') ||
                                 document.querySelector('button[data-control-name="send"]') ||
                                 document.querySelector('button:contains("Send")');

                if (sendButton) {
                    sendButton.click();
                    await this.delay(2000);
                }
            }
        } catch (error) {
            console.error('Error sending connection request:', error);
        }
    }

    async sendDirectMessage(profile, template) {
        try {
            // Look for Message button
            const messageButton = document.querySelector('[data-control-name="message"]') ||
                                document.querySelector('button[aria-label*="Message"]') ||
                                document.querySelector('button:contains("Message")');

            if (messageButton) {
                messageButton.click();
                await this.delay(2000);

                // Find the message composer
                const messageComposer = document.querySelector('.msg-form__contenteditable') ||
                                      document.querySelector('[data-artdeco-is-focused="true"]') ||
                                      document.querySelector('div[role="textbox"]');

                if (messageComposer) {
                    const personalizedMessage = this.personalizeMessage(template.content, profile);
                    messageComposer.innerHTML = personalizedMessage;
                    messageComposer.dispatchEvent(new Event('input', { bubbles: true }));
                    await this.delay(1000);

                    // Send the message
                    const sendButton = document.querySelector('[data-control-name="send"]') ||
                                     document.querySelector('button[type="submit"]') ||
                                     document.querySelector('button:contains("Send")');

                    if (sendButton) {
                        sendButton.click();
                        await this.delay(2000);
                    }
                }
            }
        } catch (error) {
            console.error('Error sending direct message:', error);
        }
    }

    personalizeMessage(template, profile) {
        let message = template;

        // Replace placeholders with profile data
        message = message.replace(/{firstName}/g, profile.firstName || profile.name.split(' ')[0]);
        message = message.replace(/{lastName}/g, profile.lastName || profile.name.split(' ').slice(1).join(' '));
        message = message.replace(/{name}/g, profile.name);
        message = message.replace(/{company}/g, profile.company || '');
        message = message.replace(/{title}/g, profile.title || '');
        message = message.replace(/{location}/g, profile.location || '');

        // Add some basic industry/field detection
        const title = profile.title.toLowerCase();
        if (title.includes('engineer') || title.includes('developer')) {
            message = message.replace(/{industry}/g, 'technology');
            message = message.replace(/{field}/g, 'software development');
        } else if (title.includes('marketing')) {
            message = message.replace(/{industry}/g, 'marketing');
            message = message.replace(/{field}/g, 'marketing');
        } else if (title.includes('sales')) {
            message = message.replace(/{industry}/g, 'sales');
            message = message.replace(/{field}/g, 'sales');
        } else {
            message = message.replace(/{industry}/g, 'your industry');
            message = message.replace(/{field}/g, 'your field');
        }

        return message;
    }

    getRandomDelay(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
}

// Initialize the automation when the page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new LinkedInAutomation();
    });
} else {
    new LinkedInAutomation();
}
