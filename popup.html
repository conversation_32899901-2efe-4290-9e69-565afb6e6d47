<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Auto Connect & Message</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>LinkedIn Automation</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span id="statusText">Ready</span>
            </div>
        </header>

        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="campaigns">Campaigns</button>
            <button class="tab-btn" data-tab="profiles">Profiles</button>
            <button class="tab-btn" data-tab="messages">Messages</button>
            <button class="tab-btn" data-tab="analytics">Analytics</button>
            <button class="tab-btn" data-tab="settings">Settings</button>
        </nav>

        <!-- Campaigns Tab -->
        <div class="tab-content active" id="campaigns">
            <div class="section">
                <h2>Create New Campaign</h2>
                <form id="campaignForm">
                    <div class="form-group">
                        <label for="campaignName">Campaign Name:</label>
                        <input type="text" id="campaignName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="sourceMethod">Profile Discovery Method:</label>
                        <select id="sourceMethod" required>
                            <option value="">Select Method</option>
                            <option value="search">LinkedIn Search</option>
                            <option value="sales-navigator">Sales Navigator</option>
                            <option value="connections">Connection Search</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="targetCount">Target Profile Count:</label>
                        <input type="number" id="targetCount" min="1" max="100" value="50">
                    </div>

                    <button type="submit" class="btn btn-primary">Create Campaign</button>
                </form>
            </div>

            <div class="section">
                <h2>Active Campaigns</h2>
                <div id="campaignsList" class="campaigns-list">
                    <!-- Campaigns will be populated here -->
                </div>
            </div>
        </div>

        <!-- Profiles Tab -->
        <div class="tab-content" id="profiles">
            <div class="section">
                <h2>Profile Discovery</h2>
                <div class="discovery-controls">
                    <button id="startDiscovery" class="btn btn-primary">Start Profile Discovery</button>
                    <button id="stopDiscovery" class="btn btn-secondary">Stop</button>
                </div>
                <div id="discoveryProgress" class="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span id="progressText">0 profiles found</span>
                </div>
            </div>

            <div class="section">
                <h2>Discovered Profiles</h2>
                <div class="profile-filters">
                    <input type="text" id="profileSearch" placeholder="Search profiles...">
                    <button id="selectAll" class="btn btn-small">Select All</button>
                    <button id="deselectAll" class="btn btn-small">Deselect All</button>
                </div>
                <div id="profilesList" class="profiles-list">
                    <!-- Profiles will be populated here -->
                </div>
            </div>
        </div>

        <!-- Messages Tab -->
        <div class="tab-content" id="messages">
            <div class="section">
                <h2>Message Templates</h2>
                <div class="template-controls">
                    <button id="addTemplate" class="btn btn-primary">Add Template</button>
                    <button id="generateAI" class="btn btn-secondary">AI Generate</button>
                </div>
                <div id="messageTemplates" class="templates-list">
                    <!-- Templates will be populated here -->
                </div>
            </div>

            <div class="section">
                <h2>Send Messages</h2>
                <div class="send-controls">
                    <button id="startMessaging" class="btn btn-primary">Start Messaging</button>
                    <button id="stopMessaging" class="btn btn-secondary">Stop</button>
                </div>
                <div id="messagingProgress" class="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="messageProgressFill"></div>
                    </div>
                    <span id="messageProgressText">0 messages sent</span>
                </div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-content" id="analytics">
            <div class="section">
                <h2>Campaign Analytics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Sent</h3>
                        <span id="totalSent" class="stat-number">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>Accepted</h3>
                        <span id="totalAccepted" class="stat-number">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>Response Rate</h3>
                        <span id="responseRate" class="stat-number">0%</span>
                    </div>
                    <div class="stat-card">
                        <h3>Today's Sent</h3>
                        <span id="todaySent" class="stat-number">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-content" id="settings">
            <div class="section">
                <h2>Automation Settings</h2>
                <div class="form-group">
                    <label for="dailyLimit">Daily Message Limit:</label>
                    <input type="number" id="dailyLimit" min="1" max="100" value="20">
                </div>
                
                <div class="form-group">
                    <label for="delayMin">Min Delay (seconds):</label>
                    <input type="number" id="delayMin" min="5" max="300" value="30">
                </div>
                
                <div class="form-group">
                    <label for="delayMax">Max Delay (seconds):</label>
                    <input type="number" id="delayMax" min="10" max="600" value="60">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoConnect"> Send Connection Request
                    </label>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoMessage"> Send Message After Connection
                    </label>
                </div>

                <button id="saveSettings" class="btn btn-primary">Save Settings</button>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
